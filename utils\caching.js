const cache = require('../config/caching');
const logger = require('../config/logger');
const { col } = require("sequelize");

/**
 * Retrieves the event configuration for a given event type from cache.
 * If the configuration is not found in the cache, it loads the configuration
 * from the database, caches it with a specified TTL, and then returns it.
 *
 * @param {string} eventType - The event type for which to retrieve the configuration.
 * @returns {Promise<Object|null>} A promise that resolves to the event configuration object
 *                                  (as a plain object) if found, or null if not found.
 */
const getCachedEventConfig = async (eventType) => {
  const eventConfigKey = `eventConfig:${eventType}`;

  let eventConfig = await cache.get(eventConfigKey);
  if (eventConfig) {
    return eventConfig;
  }

  const { EventConfig } = require("../models");
  eventConfig = await EventConfig.findOne({ where: { event: eventType } });

  if (eventConfig) {
    eventConfig = eventConfig.get({ plain: true });
    // Set the config in cache for 10 hours (3600000 mili seconds)
    await cache.set(eventConfigKey, eventConfig, 3600000 * 10);
  } else {
    logger.error(`No EventConfig found in DB for event type: ${eventType}`);
  }
  return eventConfig;
};

/**
 * Retrieves the function mapping for a given queue name, function name, or function ID from cache.
 * If not found, loads from DB, caches for 10 hours, then returns it.
 *
 * @param {Object} params
 * @param {string} [params.queue] - The queue name to look up.
 * @param {string} [params.name]  - The function name to look up.
 * @param {string} [params.id]    - The function ID to look up.
 * @returns {Promise<Object|null>} - The function record as a plain object, or null.
 */
const getCachedFunction = async ({ queue = null, name = null, id = null }) => {
  if (!queue && !name && !id) {
    throw new Error("Must supply at least one of `queue`, `name`, or `id` to getCachedFunction");
  }

  let key;
  let where;

  if (queue) {
    key = `queueFunctionByQueue:${queue}`;
    where = { queue };
  } else if (name) {
    key = `queueFunctionByName:${name}`;
    where = { name };
  } else {
    key = `queueFunctionById:${id}`;
    where = { function_id: id };
  }

  let cached = await cache.get(key);
  if (cached) {
    return cached;
  }

  const { Function } = require("../models");
  const instance = await Function.findOne({ where });

  if (!instance) {
    logger.error(`No Function found in DB for ${queue ? `queue="${queue}"` : name ? `name="${name}"` : `id="${id}"`}`);
    return null;
  }

  const plainObject = instance.get({ plain: true });

  // Cache for 10 hours (3600 seconds * 10)
  await cache.set(key, plainObject, 3600 * 10);

  return plainObject;
};


/**
 * Retrieves the application_type mapping for a given application name or id from cache.
 * If not found, loads from DB, caches for 10 hours, then returns it.
 *
 * @param {Object}   params
 * @param {string}  [params.id]   - The application-type ID to look up.
 * @param {string}  [params.name] - The application name to look up.
 * @returns {Promise<Object|null>} - The application-type record as a plain object, or null.
 */
const getCachedApplication = async ({ name = null, id = null }) => {
  if (!name && !id) {
    throw new Error("Must supply either `name` or `id` to getCachedApplication");
  }

  const key = `applicationTypeBy${name ? "Name" : "Id"}:${name || id}`;

  let cached = await cache.get(key);
  if (cached) {
    return cached;
  }

  const { ApplicationType } = require("../models");
  const where = name
    ? { name }
    : { application_type_id: id };

  const instance = await ApplicationType.findOne({ where });

  if (!instance) {
    logger.error(
      `No Application Type found in DB for ${name ? `name="${name}"` : `id="${id}"`}`
    );
    return null;
  }

  const plainObject = instance.get({ plain: true });

  // Cache the result for 10 hours (3600 seconds * 10)
  await cache.set(key, plainObject, 3600 * 10);

  return plainObject;
};


/**
 * Look up a MasterData.key by group+value, with per‐group caching.
 * @param {string} group
 * @param {string} value
 * @returns {Promise<number|null>} the integer key, or null if not found
 */
async function getCachedMasterDataValue(group, value) {
  const { MasterData } = require("../models");
  const cacheKey = `masterDataGroup:${group}`;
  let items = await cache.get(cacheKey);
  if (!items) {
    // load all for this group once
    const rows = await MasterData.findAll({
      where: { group },
      attributes: ['key', 'value'],
      raw: true
    });
    items = rows.map(r => ({ key: r.key, value: r.value }));
    await cache.set(cacheKey, items, 3600000); // 1 hour TTL
    if (items.length === 0) {
      logger.warn(`No MasterData found for group "${group}"`);
    }
  }

  const rec = items.find(i => i.value === value);
  return rec ? rec.key : null;
}

/**
 * Retrieves the notification configuration for a given notification type from cache.
 * If the configuration is not found in the cache, it loads the configuration
 * from the database, caches it with a specified TTL, and then returns it.
 *
 * @param {string} notificationName - The notification name for which to retrieve the configuration.
 * @returns {Promise<Object|null>} A promise that resolves to the notification configuration object
 *                                  (as a plain object) if found, or null if not found.
 */
const getCachedNotificationConfig = async (notificationName) => {
  const notificationConfigKey = `notificationConfig:${notificationName}`;

  let notificationConfig = await cache.get(notificationConfigKey);
  if (notificationConfig) {
    return notificationConfig;
  }

  // Fetch from notification channel model where notification instance have name as notificationName
  const { NotificationChannel, Notification } = require("../models");
  const notificationConfigData = await NotificationChannel.findAll({
    attributes: ["channel", "notification_child_id", [col("notification.schema"), "schema"], [col("notification.schema_column"), "schema_column"],],
    include: [
      {
        model: Notification,
        as: "notification",
        where: { name: notificationName },
        attributes: [],
      },
    ],
  });

  if (!notificationConfigData) {
    logger.error(`No Notification found in DB for name: ${notificationName}`);
    return null;
  }

  // Cache the result for 10 hours
  await cache.set(notificationConfigKey, notificationConfigData, 3600000 * 10);
  return notificationConfigData;
};

/**
 * Retrieves a NotificationEmail by its primary key with caching.
 *
 * @param {string} id - The primary key of the NotificationEmail record.
 * @returns {Promise<Object|null>} The notification email data or null if not found.
 */
const getCachedNotificationEmail = async (id) => {
  const notificationEmailKey = `notificationEmail:${id}`;

  // Try to get from cache
  let notificationEmail = await cache.get(notificationEmailKey);
  if (notificationEmail) {
    return notificationEmail;
  }

  // Fetch from DB
  const { NotificationEmail } = require("../models");
  const notificationEmailData = await NotificationEmail.findByPk(id, {
    attributes: ["notification_email_id", "receiver_column", "subject", "template"],
  });

  if (!notificationEmailData) {
    logger.error(`No notification email data found in DB for primary key: ${id}`);
    return null;
  }

  // Cache for 10 hours
  await cache.set(notificationEmailKey, notificationEmailData, 3600000 * 10);
  return notificationEmailData;
};

/**
 * Retrieves a NotificationText by its primary key with caching.
 *
 * @param {string} id - The primary key of the NotificationText record.
 * @returns {Promise<Object|null>} The notification text data or null if not found.
 */
const getCachedNotificationText = async (id) => {
  const notificationTextKey = `notificationText:${id}`;

  // Try to get from cache
  let notificationText = await cache.get(notificationTextKey);
  if (notificationText) {
    return notificationText;
  }

  // Fetch from DB
  const { NotificationText } = require("../models");
  const notificationTextData = await NotificationText.findByPk(id, {
    attributes: ["notification_text_id", "receiver_column", "template"],
  });

  if (!notificationTextData) {
    logger.error(`No notification text data found in DB for primary key: ${id}`);
    return null;
  }

  // Cache for 10 hours
  await cache.set(notificationTextKey, notificationTextData, 3600000 * 10);
  return notificationTextData;
};

/**
 * Retrieves the language by its name from cache.
 * If not found, loads from DB, caches for 10 hours, then returns it.
 *
 * @param {string} name - The name of the language to look up.
 * @returns {Promise<Object|null>} - The language record as a plain object, or null.
 */
const getCachedLanguageByName = async (name) => {
  const key = `languageByName:${name}`;

  let cached = await cache.get(key);
  if (cached) {
    return cached;
  }

  const { Language } = require("../models");
  const instance = await Language.findOne({ where: { name } });

  if (!instance) {
    logger.error(`No Language found in DB for name: ${name}`);
    return null;
  }

  const plainObject = instance.get({ plain: true });

  // Cache for 10 hours (3600 seconds * 10)
  await cache.set(key, plainObject, 3600 * 10);

  return plainObject;
};

/**
 * Retrieves all active languages (status = true) from the Language model.
 * If the data is not found in the cache, it loads from the database,
 * caches it for 10 hours, and then returns it.
 *
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of active languages.
 */
const getActiveLanguagesFromCache = async () => {
  const cacheKey = "activeLanguages";
  let activeLanguages = await cache.get(cacheKey);

  if (!activeLanguages) {
    const { Language } = require("../models");
    activeLanguages = await Language.findAll({
      where: { status: true },
    });

    // Cache the result for 10 hours (3600 seconds * 10)
    await cache.set(cacheKey, activeLanguages, 3600 * 10);
  }

  return activeLanguages;
};

/**
 * Retrieves active agent configurations with their settings from cache.
 * If not found, loads from DB, caches for 10 hours, then returns them.
 * If type is provided, filters by agent type. If type is null, returns all.
 *
 * @param {string|null} type - Agent type to filter by (e.g., 'inbound', 'outbound'). If null, returns all.
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of active agent configurations with settings.
 */
const getCachedAgentSources = async (type = null) => {
  const cacheKey = 'activeAgentSources';

  let agentSources = await cache.get(cacheKey);
  if (!agentSources) {
    const { Agent, AgentSetting } = require('../models');
    const { safeDecrypt } = require('./encryption');

    const agents = await Agent.findAll({
      where: { status: true },
      include: [{
        model: AgentSetting,
        as: 'settings',
        attributes: ['key', 'value', 'key_name', 'is_encrypted']
      }]
    });

    if (agents) {
      agentSources = agents.map(agent => {
        const plainAgent = agent.get({ plain: true });
        // Convert settings array to object for easier access with decryption
        const settingsObj = {};
        if (plainAgent.settings) {
          plainAgent.settings.forEach(setting => {
            // Decrypt the value if it's marked as encrypted
            settingsObj[setting.key] = safeDecrypt(setting.value, setting.is_encrypted);
          });
        }
        return {
          ...plainAgent,
          settingsObj
        };
      });
      await cache.set(cacheKey, agentSources, 3600000 * 10); // Cache for 10 hours
    }
  }
  if (type) {
    return (agentSources || []).filter(agent => agent.type === type);
  }
  return agentSources || [];
};


async function getCachedAgentConfigs() {
  const key = 'agentConfigs';
  let configs = await cache.get(key);
  if (!configs) {
    const { Agent, AgentSetting } = require('../models');
    const { safeDecrypt } = require('./encryption');

    const rows = await Agent.findAll({
      include: [{
        model: AgentSetting,
        as: 'settings',
        attributes: ['key', 'value', 'key_name', 'is_encrypted']
      }]
    });
    configs = rows.map(agent => {
      const plainAgent = agent.get({ plain: true });
      // Convert settings array to object for easier access with decryption
      const settingsObj = {};
      if (plainAgent.settings) {
        plainAgent.settings.forEach(setting => {
          // Decrypt the value if it's marked as encrypted
          settingsObj[setting.key] = safeDecrypt(setting.value, setting.is_encrypted);
        });
      }
      return {
        ...plainAgent,
        settingsObj
      };
    });
    await cache.set(key, configs, 3600000); // 1 hour
  }
  return configs;
}

/**
 * Retrieves active cron configurations from cache.
 * If not found, loads from DB, caches for 10 hours, then returns them.
 *
 * @returns {Promise<Array<Object>>} A promise that resolves to an array of active cron configurations.
 */
const getCachedCronConfigs = async () => {
  const cacheKey = 'activeCronConfigs';

  let cronConfigs = await cache.get(cacheKey);
  if (!cronConfigs) {
    const { CronConfig } = require('../models');

    cronConfigs = await CronConfig.findAll({
      where: { is_active: true },
      raw: true
    });

    if (cronConfigs) {
      await cache.set(cacheKey, cronConfigs, 3600000 * 10); // Cache for 10 hours
    }
  }

  return cronConfigs || [];
};

module.exports = { getCachedEventConfig, getCachedFunction, getCachedMasterDataValue,
  getCachedApplication, getCachedNotificationConfig, getCachedNotificationEmail,
  getCachedNotificationText, getCachedLanguageByName, getActiveLanguagesFromCache,
  getCachedAgentSources, getCachedAgentConfigs, getCachedCronConfigs };